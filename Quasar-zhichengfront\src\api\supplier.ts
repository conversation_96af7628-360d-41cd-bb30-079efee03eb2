import request from 'src/api/request';

// 供应商接口定义
export interface Supplier {
  id?: number;
  supplier_code: string;
  supplier_name: string;
  supplier_type: string;
  type_display?: string;
  contact_person?: string;
  phone?: string;
  mobile?: string;
  email?: string;
  fax?: string;
  region?: string;
  address?: string;
  postal_code?: string;
  credit_limit?: number;
  payment_terms?: string;
  tax_number?: string;
  industry?: string;
  company_size?: string;
  website?: string;
  main_products?: string;
  quality_certification?: string;
  delivery_capability?: string;
  status: number;
  status_display?: string;
  remark?: string;
  team?: number;
  creator?: number;
  creator_name?: string;
  team_name?: string;
  create_datetime?: string;
  update_datetime?: string;
}

// 供应商列表查询参数接口
export interface SupplierListParams {
  page?: number;
  page_size?: number;
  search?: string;
  status?: number;
  supplier_type?: string;
  region?: string;
  industry?: string;
  team?: number;
  creator?: number;
  create_date_start?: string;
  create_date_end?: string;
  credit_limit_min?: number;
  credit_limit_max?: number;
  contact_person?: string;
  phone?: string;
  main_products?: string;
  ordering?: string;
}

// 供应商选择项接口
export interface SupplierChoices {
  status_choices: Array<{value: number, label: string}>;
  type_choices: Array<{value: string, label: string}>;
  region_choices: string[];
  industry_choices: string[];
}

// 供应商统计接口
export interface SupplierStatistics {
  total_count: number;
  active_count: number;
  inactive_count: number;
  current_month_count: number;
  year_count: number;
  region_stats: Record<string, number>;
  type_stats: Record<string, number>;
}

// 供应商导出参数接口
export interface SupplierExportParams {
  start_date?: string;
  end_date?: string;
  region?: string;
  status?: number;
  supplier_type?: string;
  team_id?: number;
  creator_id?: number;
  export_format?: 'excel';
}

// 获取供应商列表
export const getSuppliersApi = (params?: SupplierListParams) => {
  return request({ url: '/suppliers/', method: 'get', params });
};

// 获取供应商详情
export const getSupplierApi = (id: number) => {
  return request({ url: `/suppliers/${id}/`, method: 'get' });
};

// 创建供应商
export const createSupplierApi = (data: Partial<Supplier>) => {
  return request({ url: '/suppliers/', method: 'post', data });
};

// 更新供应商
export const updateSupplierApi = (id: number, data: Partial<Supplier>) => {
  return request({ url: `/suppliers/${id}/`, method: 'put', data });
};

// 删除供应商
export const deleteSupplierApi = (id: number) => {
  return request({ url: `/suppliers/${id}/`, method: 'delete' });
};

// 获取供应商统计信息
export const getSupplierStatisticsApi = () => {
  return request({ url: '/suppliers/statistics/', method: 'get' });
};

// 获取供应商选择项
export const getSupplierChoicesApi = () => {
  return request({ url: '/suppliers/choices/', method: 'get' });
};

// 导出供应商到Excel
export const exportSuppliersToExcelApi = (params: SupplierExportParams) => {
  return request({
    url: '/suppliers/export_excel/',
    method: 'post',
    data: params,
    responseType: 'blob'
  });
};

// 批量删除供应商 - 使用通用批量删除接口
export const batchDeleteSuppliersApi = (ids: number[]) => {
  return request({ url: '/api/BatchDelete/generic-batch-delete/', method: 'post', data: { model: 'supplier', ids } });
};

// 批量操作供应商
export const batchOperateSuppliersApi = (ids: number[], action: 'update_status', status?: number) => {
  return request({
    url: '/suppliers/batch_operation/',
    method: 'post',
    data: {
      ids,
      action,
      status
    }
  });
};

// 导出所有API函数
export {
    batchDeleteSuppliersApi,
    batchOperateSuppliersApi, createSupplierApi, deleteSupplierApi, exportSuppliersToExcelApi, getSupplierApi, getSupplierChoicesApi, getSupplierStatisticsApi, getSuppliersApi, updateSupplierApi
};

