<template>
  <q-dialog v-model="showDialog" persistent maximized>
    <q-card class="full-width">
      <!-- 标题栏 -->
      <q-card-section class="row items-center q-pb-none">
        <div class="text-h6">客户选择列表</div>
        <q-space />
        <div class="row q-gutter-sm col-auto">
          <q-btn color="primary" icon="add" label="新建客户" @click="createCustomer" />
          <q-btn
            color="primary"
            icon="checklist"
            label="确认选择"
            @click="handleConfirm"
            :disabled="!selected.length"
          />
        </div>
        <q-btn icon="close" flat round dense v-close-popup />
      </q-card-section>
      <!-- 搜索和筛选区域 -->
      <q-card-section>
        <div class="row q-gutter-md">
          <div class="col-12 col-md-3">
            <q-input
              v-model="searchParams.search"
              label="搜索客户名称/编号/联系人"
              outlined
              dense
              clearable
              @keyup.enter="debouncedLoadCustomers"
            >
              <template v-slot:append>
                <q-icon name="search" @click="loadCustomers" class="cursor-pointer" />
              </template>
            </q-input>
          </div>
          <div class="col-12 col-md-2">
            <q-select
              v-model="searchParams.status"
              label="状态"
              :options="statusOptions"
              outlined
              dense
              clearable
              emit-value
              map-options
              @update:model-value="loadCustomers"
            />
          </div>
          <div class="col-12 col-md-2">
            <q-input
              v-model="searchParams.region"
              label="地区"
              outlined
              dense
              clearable
              @keyup.enter="debouncedLoadCustomers"
            />
          </div>
          <div class="col-12 col-md-2">
            <q-select
              v-model="searchParams.customer_type"
              label="客户类型"
              :options="typeOptions"
              outlined
              dense
              clearable
              emit-value
              map-options
              @update:model-value="loadCustomers"
            />
          </div>
          <div class="col-12 col-md-2">
            <q-input
              v-model="searchParams.industry"
              label="行业"
              outlined
              dense
              clearable
              @keyup.enter="debouncedLoadCustomers"
            />
          </div>
        </div>
      </q-card-section>

      <!-- 超时提示 -->
      <q-banner v-if="isTimeout" class="bg-warning text-white q-mb-sm">
        <template v-slot:avatar>
          <q-icon name="warning" />
        </template>
        网络请求超时，已切换到模拟数据模式。请检查网络连接或稍后重试。
        <template v-slot:action>
          <q-btn flat label="重试" @click="retryLoad" />
        </template>
      </q-banner>

      <!-- 客户表格区域 -->
      <q-scroll-area style="height: 60vh;">
        <q-table
          class="q-table-container"
          :rows="customers"
          :columns="columns"
          :loading="loading"
          v-model:pagination="pagination"
          row-key="id"
          binary-state-sort
          selection="multiple"
          v-model:selected="selected"
          :selected-rows-key="customerSelectionKey"
          :rows-per-page-options="[10,20,50,100]"
          @request="onRequest"
        >
          <!-- 序号列自定义渲染 -->
          <template v-slot:body-cell-index="props">
            <q-td :props="props">
              {{ calculateRowNumber(props.rowIndex) }}
              <q-tooltip>
                页码: {{ pagination.page }}<br>
                每页记录数: {{ pagination.rowsPerPage }}<br>
                行索引: {{ props.rowIndex }}<br>
                计算公式: ({{ pagination.page }} - 1) * {{ pagination.rowsPerPage }} + {{ props.rowIndex }} + 1
              </q-tooltip>
            </q-td>
          </template>

          <!-- 操作列 -->
          <template v-slot:body-cell-actions="props">
            <q-td :props="props">
              <q-btn
                flat
                dense
                icon="visibility"
                color="primary"
                @click="viewCustomer(props.row)"
                size="sm"
              >
                <q-tooltip>查看详情</q-tooltip>
              </q-btn>
            </q-td>
          </template>

          <!-- 状态列 -->
          <template v-slot:body-cell-status_display="props">
            <q-td :props="props">
              <q-chip :color="getStatusColor(props.row.status)" text-color="white" dense>
                {{ props.value }}
              </q-chip>
            </q-td>
          </template>

          <!-- 客户类型列 -->
          <template v-slot:body-cell-type_display="props">
            <q-td :props="props">
              <q-badge :color="getTypeColor(props.row.customer_type)" :label="props.value" />
            </q-td>
          </template>
        </q-table>
      </q-scroll-area>
    </q-card>

    <!-- 客户详情弹窗 -->
    <CustomerDialog
      v-model="showDetailDialog"
      :mode="dialogMode"
      :customer-data="currentCustomer"
      @saved="onCustomerSaved"
    />
  </q-dialog>
</template>

<script setup lang="ts">
import { debounce, useQuasar } from 'quasar';
import CustomerDialog from 'src/components/customer/CustomerDialog.vue';
import { computed, onMounted, reactive, ref } from 'vue';
import { customerApi, type Customer, type CustomerChoices } from '../../api/customer';

defineOptions({ name: 'CustomerSelectDialog' });

// 类型定义
interface Pagination {
  sortBy: string;
  descending: boolean;
  page: number;
  rowsPerPage: number;
  rowsNumber: number;
}

// 核心变量
const $q = useQuasar();
const showDialog = ref(false); // 控制弹窗显示
const loading = ref(false);
const showDetailDialog = ref(false); // 详情弹窗控制
const currentCustomer = ref<Customer | null>(null);
const dialogMode = ref<'create' |'view' | 'edit'>('view');
const customerSelectionKey = 'id'; // 选择键（客户ID）
// 数据存储
const customers = ref<Customer[]>([]);
const choices = ref<CustomerChoices>({
  status_choices: [],
  type_choices: [],
  region_choices: [],
  industry_choices: [],
});

// 选中的客户列表（多选）
const selected = ref<Customer[]>([]);

// 网络状态
const isTimeout = ref(false);
const REQUEST_TIMEOUT = 10000; // 10秒超时

// 搜索参数
const searchParams = reactive({
  search: '',
  status: null as number | null,
  customer_type: '',
  region: '',
  industry: '',
});

// 分页配置
const pagination = ref<Pagination>({
  sortBy: 'create_datetime',
  descending: true,
  page: 1,
  rowsPerPage: 20,
  rowsNumber: 0,
});

// 模拟数据
const mockCustomers: Customer[] = [
  {
    id: 1,
    customer_code: 'CUST001',
    customer_name: '客户A公司',
    customer_type: 'manufacturer',
    type_display: '制造商',
    contact_person: '张三',
    phone: '010-12345678',
    mobile: '13800138000',
    email: '<EMAIL>',
    region: '北京',
    address: '北京市朝阳区xxx街道',
    status: 1,
    status_display: '活跃',
    creator_name: '李四',
    team_name: '采购团队',
    create_datetime: '2024-12-01T10:00:00Z'
  },
  {
    id: 2,
    customer_code: 'CUST002',
    customer_name: '客户B企业',
    customer_type: 'distributor',
    type_display: '分销商',
    contact_person: '王五',
    phone: '021-87654321',
    mobile: '13900139000',
    email: '<EMAIL>',
    region: '上海',
    address: '上海市浦东新区xxx大厦',
    status: 1,
    status_display: '活跃',
    creator_name: '赵六',
    team_name: '销售团队',
    create_datetime: '2024-11-15T09:30:00Z'
  },
  {
    id: 3,
    customer_code: 'CUST003',
    customer_name: '客户C机构',
    customer_type: 'service',
    type_display: '服务商',
    contact_person: '孙七',
    phone: '020-56789012',
    mobile: '13700137000',
    email: '<EMAIL>',
    region: '广东',
    address: '广州市天河区xxx路',
    status: 2,
    status_display: '待审核',
    creator_name: '周八',
    team_name: '市场团队',
    create_datetime: '2024-10-20T14:20:00Z'
  }
];

// 表格列定义
const columns = computed(() => [
  {
    name: 'index',
    required: true,
    label: '序号',
    align: 'left',
    field: 'index',
    sortable: true
  },
  {
    name: 'customer_code',
    required: true,
    label: '客户代码',
    align: 'left',
    field: (row: Customer) => row.customer_code,
    sortable: true
  },
  {
    name: 'customer_name',
    required: true,
    label: '客户名称',
    align: 'left',
    field: (row: Customer) => row.customer_name,
    sortable: true
  },
  {
    name: 'type_display',
    label: '客户类型',
    align: 'center',
    field: (row: Customer) => row.type_display,
    sortable: true
  },
  {
    name: 'contact_person',
    label: '联系人',
    align: 'left',
    field: (row: Customer) => row.contact_person
  },
  {
    name: 'region',
    label: '地区',
    align: 'left',
    field: (row: Customer) => row.region
  },
  {
    name: 'status_display',
    label: '状态',
    align: 'center',
    field: (row: Customer) => row.status_display
  },
  {
    name: 'create_datetime',
    label: '创建时间',
    align: 'left',
    field: (row: Customer) => row.create_datetime,
    format: (val: string) => new Date(val).toLocaleString(),
    sortable: true
  },
  {
    name: 'actions',
    label: '操作',
    align: 'center',
    field: 'actions'
  }
]);

// 筛选选项
const statusOptions = computed(() =>
  choices.value.status_choices.map(choice => ({
    label: choice.label,
    value: choice.value
  }))
);

const typeOptions = computed(() =>
  choices.value.type_choices.map(choice => ({
    label: choice.label,
    value: choice.value
  }))
);

// 状态颜色映射
const getStatusColor = (status: number) => {
  const colors: Record<number, string> = {
    0: 'grey',      // 禁用
    1: 'positive',  // 活跃
    2: 'orange',    // 待审核
    3: 'negative'   // 已冻结
  };
  return colors[status] || 'grey';
};

// 类型颜色映射
const getTypeColor = (type: string) => {
  const colors: Record<string, string> = {
    'manufacturer': 'blue',    // 制造商
    'distributor': 'green',    // 分销商
    'service': 'purple',       // 服务商
    'material': 'orange',      // 材料商
    'other': 'grey'            // 其他
  };
  return colors[type] || 'grey';
};

// 计算序号
const calculateRowNumber = (rowIndex: number) => {
  return (pagination.value.page - 1) * pagination.value.rowsPerPage + rowIndex + 1;
};

// 加载客户数据
const loadCustomers = async () => {
  loading.value = true;
  isTimeout.value = false;

  try {
    // 超时控制
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('请求超时')), REQUEST_TIMEOUT);
    });

    const apiPromise = customerApi.getCustomerList({
      ...searchParams,
      page: pagination.value.page,
      page_size: pagination.value.rowsPerPage,
      ordering: pagination.value.descending
        ? `-${pagination.value.sortBy}`
        : pagination.value.sortBy
    });

    const response = await Promise.race([apiPromise, timeoutPromise]);
    const typedResponse = response as { data: { results: Customer[], count: number } };
    customers.value = typedResponse.data.results || [];
    pagination.value.rowsNumber = typedResponse.data.count || 0;

  } catch (error) {
    console.error('加载客户数据失败:', error);
    isTimeout.value = true;
    customers.value = mockCustomers;
    pagination.value.rowsNumber = mockCustomers.length;

    $q.notify({
      type: 'warning',
      message: '网络请求超时，已切换到模拟数据模式',
      position: 'top',
      timeout: 3000
    });
  } finally {
    loading.value = false;
  }
};

// 重试加载
const retryLoad = async () => {
  isTimeout.value = false;
  await loadCustomers();
};

const createCustomer = () => {
  dialogMode.value = 'create';
  showDetailDialog.value = true;
};
// 查看客户详情
const viewCustomer = (customer: Customer) => {
  dialogMode.value = 'view';
  currentCustomer.value = { ...customer };
  showDetailDialog.value = true;
};

// 防抖加载
const debouncedLoadCustomers = debounce(loadCustomers, 500);

// 表格请求处理（分页、排序）
const onRequest = async (props: { pagination: Pagination }) => {
  const { page, rowsPerPage, sortBy, descending } = props.pagination;
  pagination.value.page = page;
  pagination.value.rowsPerPage = rowsPerPage;
  pagination.value.sortBy = sortBy;
  pagination.value.descending = descending;
  await loadCustomers();
};

// 确认选择（核心：提取选中客户的ID集）
const handleConfirm = () => {
  if (selected.value.length) {
    // 提取选中客户的ID组成数组
    const selectedIds = selected.value.map(customer => customer.id);
    // 触发事件返回ID集
    emits('select', selectedIds);
    // 关闭弹窗并重置选择状态
    showDialog.value = false;
    selected.value = [];
    $q.notify({
      type: 'positive',
      message: `已选择 ${selectedIds.length} 个客户`,
      position: 'top',
      timeout: 2000
    });
  }
};

// 客户保存回调
const onCustomerSaved = async () => {
  showDetailDialog.value = false;
  await loadCustomers(); // 刷新数据
  $q.notify({
    type: 'positive',
    message: '客户信息已更新',
    position: 'top',
    timeout: 2000
  });
};

// 加载选项数据
const loadChoices = async () => {
  try {
    const response = await customerApi.getCustomerChoices();
    if (response?.data) {
      choices.value = response.data;
    }
  } catch (error) {
    console.error('加载选择项失败:', error);
    $q.notify({
      type: 'negative',
      message: '加载筛选选项失败',
      position: 'top',
      timeout: 2000
    });
  }
};

// 暴露事件：选择完成后返回ID集
const emits = defineEmits<{
  (e: 'select', customerIds: number[]): void;
  (e: 'close'): void;
}>();

// 提供打开弹窗的方法
const openDialog = async () => {
  showDialog.value = true;
  selected.value = []; // 重置选择状态
  // 可选：每次打开弹窗时刷新数据
  await loadCustomers();
};

// 初始化
onMounted(() => {
  loadChoices();
});

// 暴露方法给父组件
defineExpose({ openDialog });


</script>

<style scoped>
.q-table-container {
  min-width: 900px; /* 确保表格在小屏幕下可滚动 */
}
</style>
