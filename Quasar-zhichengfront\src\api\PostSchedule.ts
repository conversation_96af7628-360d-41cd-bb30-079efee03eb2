import request from 'src/api/request';

// 印后排期接口定义
export interface PostSchedule {
  id?: number;
  schedule_id: string;
  engineering_id: number;
  engineering_code: string;
  product_name: string;
  quantity: number;
  priority: number;
  priority_display?: string;
  is_print?: boolean;
  is_lamina?: boolean;
  is_hot_stamp?: boolean;
  is_convex?: boolean;
  is_silk_screen?: boolean;
  is_mounted?: boolean;
  is_die_cut?: boolean;
  is_paste_window?: boolean;
  is_stick?: boolean;
  is_pack?: boolean;
  start_date?: string;
  end_date?: string;
  operator?: string;
  machine?: string;
  status: string;
  status_display?: string;
  remark?: string;
  process_status?: string;
  team?: number;
  creator?: number;
  creator_name?: string;
  team_name?: string;
  create_datetime?: string;
  update_datetime?: string;
}

// 印后排期列表查询参数接口
export interface PostScheduleListParams {
  page?: number;
  page_size?: number;
  search?: string;
  status?: string;
  priority?: number;
  engineering_code?: string;
  product_name?: string;
  operator?: string;
  machine?: string;
  team?: number;
  creator?: number;
  start_date_start?: string;
  start_date_end?: string;
  end_date_start?: string;
  end_date_end?: string;
  create_date_start?: string;
  create_date_end?: string;
  quantity_min?: number;
  quantity_max?: number;
  is_print?: boolean;
  is_lamina?: boolean;
  is_hot_stamp?: boolean;
  is_convex?: boolean;
  is_silk_screen?: boolean;
  is_mounted?: boolean;
  is_die_cut?: boolean;
  is_paste_window?: boolean;
  is_stick?: boolean;
  is_pack?: boolean;
  ordering?: string;
}

// 印后排期选择项接口
export interface PostScheduleChoices {
  status_choices: Array<{value: string, label: string}>;
  priority_choices: Array<{value: number, label: string}>;
  operator_choices: string[];
  machine_choices: string[];
}

// 印后排期统计接口
export interface PostScheduleStatistics {
  total_count: number;
  pending_count: number;
  in_progress_count: number;
  completed_count: number;
  current_month_count: number;
  current_week_count: number;
  priority_stats: Record<string, number>;
  operator_stats: Record<string, number>;
}

// 印后排期导出参数接口
export interface PostScheduleExportParams {
  start_date?: string;
  end_date?: string;
  status?: string;
  priority?: number;
  operator?: string;
  team_id?: number;
  creator_id?: number;
  export_format?: 'excel';
}

// 印后排期报表参数接口
export interface PostScheduleReportParams {
  start_date?: string;
  end_date?: string;
  status?: string;
  priority?: number;
  operator?: string;
  team_id?: number;
  creator_id?: number;
  report_format?: 'pdf' | 'html';
}

// 获取印后排期列表
export const getPostSchedulesApi = (params?: PostScheduleListParams) => {
  return request({ url: '/post_schedules/', method: 'get', params });
};

// 获取印后排期详情
export const getPostScheduleApi = (id: number) => {
  return request({ url: `/post_schedules/${id}/`, method: 'get' });
};

// 创建印后排期
export const createPostScheduleApi = (data: Partial<PostSchedule>) => {
  return request({ url: '/post_schedules/', method: 'post', data });
};

// 更新印后排期
export const updatePostScheduleApi = (id: number, data: Partial<PostSchedule>) => {
  return request({ url: `/post_schedules/${id}/`, method: 'put', data });
};

// 删除印后排期
export const deletePostScheduleApi = (id: number) => {
  return request({ url: `/post_schedules/${id}/`, method: 'delete' });
};

// 获取印后排期统计信息
export const getPostScheduleStatisticsApi = () => {
  return request({ url: '/post_schedules/statistics/', method: 'get' });
};

// 获取印后排期选择项
export const getPostScheduleChoicesApi = () => {
  return request({ url: '/post_schedules/choices/', method: 'get' });
};

// 导出印后排期到Excel
export const exportPostSchedulesToExcelApi = (params: PostScheduleExportParams) => {
  return request({
    url: '/post_schedules/export_excel/',
    method: 'post',
    data: params,
    responseType: 'blob'
  });
};

// 生成印后排期报表
export const generatePostScheduleReportApi = (params: PostScheduleReportParams) => {
  return request({
    url: '/post_schedules/generate_report/',
    method: 'post',
    data: params,
    responseType: 'blob'
  });
};

// 批量删除印后排期 - 使用通用批量删除接口
export const batchDeletePostSchedulesApi = (ids: number[]) => {
  return request({ url: '/api/BatchDelete/generic-batch-delete/', method: 'post', data: { model: 'postschedule', ids } });
};

// 批量操作印后排期
export const batchOperatePostSchedulesApi = (ids: number[], action: 'update_status' | 'move_up' | 'move_down', status?: string) => {
  return request({
    url: '/post_schedules/batch_operation/',
    method: 'post',
    data: {
      ids,
      action,
      status
    }
  });
};

// 更新印后排期字段
export const updatePostScheduleFieldApi = (id: number, field: string, value: any) => {
  return request({
    url: `/post_schedules/${id}/update_field/`,
    method: 'post',
    data: {
      field,
      value
    }
  });
};

export default {
  getPostSchedulesApi,
  getPostScheduleApi,
  createPostScheduleApi,
  updatePostScheduleApi,
  deletePostScheduleApi,
  getPostScheduleStatisticsApi,
  getPostScheduleChoicesApi,
  exportPostSchedulesToExcelApi,
  generatePostScheduleReportApi,
  batchDeletePostSchedulesApi,

  batchOperatePostSchedulesApi,
  updatePostScheduleFieldApi,
};
