import request from 'src/api/request';

// 业务绩效接口定义
export interface PerformanceData {
  id?: number;
  salesman_name: string;
  salesman_id: string;
  department: string;
  order_count: number;
  total_amount: number;
  performance_score: number;
  rank: number;
  period: string;
  year?: number;
  quarter?: number;
  month?: number;
  create_time?: string;
  update_time?: string;
}

// 业务绩效统计接口
export interface PerformanceStatistics {
  total_salesmen: number;
  total_orders: number;
  total_amount: number;
  average_performance: number;
  top_performers: Array<{
    salesman_name: string;
    performance_score: number;
  }>;
  department_stats: Array<{
    department: string;
    count: number;
    total_amount: number;
    average_performance: number;
  }>;
}

// 业务绩效列表查询参数接口
export interface PerformanceListParams {
  page?: number;
  page_size?: number;
  search?: string;
  year?: number;
  quarter?: number;
  month?: number;
  start_date?: string;
  end_date?: string;
  department?: string;
  salesman_id?: string;
  min_score?: number;
  max_score?: number;
  ordering?: string;
}

// 业务绩效导出参数接口
export interface PerformanceExportParams {
  year?: number;
  quarter?: number;
  month?: number;
  start_date?: string;
  end_date?: string;
  department?: string;
  salesman_id?: string;
  export_format?: 'excel';
}

// 业务绩效报表参数接口
export interface PerformanceReportParams {
  year?: number;
  quarter?: number;
  month?: number;
  start_date?: string;
  end_date?: string;
  department?: string;
  salesman_id?: string;
  report_format?: 'pdf' | 'html';
}

// 业务绩效选择项接口
export interface PerformanceChoices {
  year_choices: number[];
  quarter_choices: Array<{value: number, label: string}>;
  month_choices: Array<{value: number, label: string}>;
  department_choices: Array<{value: string, label: string}>;
  salesman_choices: Array<{value: string, label: string, department: string}>;
}

// 获取业务绩效列表
export const getPerformanceDataApi = (params?: PerformanceListParams) => {
  return request({ url: '/performance/', method: 'get', params });
};

// 获取业务绩效详情
export const getPerformanceDetailApi = (id: number) => {
  return request({ url: `/performance/${id}/`, method: 'get' });
};

// 创建业务绩效记录
export const createPerformanceDataApi = (data: Partial<PerformanceData>) => {
  return request({ url: '/performance/', method: 'post', data });
};

// 更新业务绩效记录
export const updatePerformanceDataApi = (id: number, data: Partial<PerformanceData>) => {
  return request({ url: `/performance/${id}/`, method: 'put', data });
};

// 删除业务绩效记录
export const deletePerformanceDataApi = (id: number) => {
  return request({ url: `/performance/${id}/`, method: 'delete' });
};

// 获取业务绩效统计信息
export const getPerformanceStatisticsApi = (params?: PerformanceListParams) => {
  return request({ url: '/performance/statistics/', method: 'get', params });
};

// 获取业务绩效选择项
export const getPerformanceChoicesApi = () => {
  return request({ url: '/performance/choices/', method: 'get' });
};

// 导出业务绩效到Excel
export const exportPerformanceToExcelApi = (params: PerformanceExportParams) => {
  return request({
    url: '/performance/export_excel/',
    method: 'post',
    data: params,
    responseType: 'blob'
  });
};

// 生成业务绩效报表
export const generatePerformanceReportApi = (params: PerformanceReportParams) => {
  return request({
    url: '/performance/generate_report/',
    method: 'post',
    data: params,
    responseType: 'blob'
  });
};

// 批量删除业务绩效 - 使用通用批量删除接口
export const batchDeletePerformanceApi = (ids: number[]) => {
  return request({ url: '/api/BatchDelete/generic-batch-delete/', method: 'post', data: { model: 'performance', ids } });
};

// 批量操作业务绩效
export const batchOperatePerformanceApi = (ids: number[], action: 'recalculate') => {
  return request({
    url: '/performance/batch_operation/',
    method: 'post',
    data: {
      ids,
      action
    }
  });
};

// 重新计算绩效
export const recalculatePerformanceApi = (params: {
  year?: number;
  quarter?: number;
  month?: number;
  start_date?: string;
  end_date?: string;
  salesman_id?: string;
}) => {
  return request({
    url: '/performance/recalculate/',
    method: 'post',
    data: params
  });
};

// 获取业务员绩效趋势
export const getPerformanceTrendApi = (salesman_id: string, params?: {
  start_date?: string;
  end_date?: string;
  period_type?: 'month' | 'quarter' | 'year';
}) => {
  return request({
    url: `/performance/trend/${salesman_id}/`,
    method: 'get',
    params
  });
};

// 获取部门绩效对比
export const getDepartmentComparisonApi = (params?: {
  year?: number;
  quarter?: number;
  month?: number;
}) => {
  return request({
    url: '/performance/department_comparison/',
    method: 'get',
    params
  });
};

// 获取绩效排行榜
export const getPerformanceRankingApi = (params?: {
  year?: number;
  quarter?: number;
  month?: number;
  limit?: number;
}) => {
  return request({
    url: '/performance/ranking/',
    method: 'get',
    params
  });
};

export default {
  getPerformanceDataApi,
  getPerformanceDetailApi,
  createPerformanceDataApi,
  updatePerformanceDataApi,
  deletePerformanceDataApi,
  getPerformanceStatisticsApi,
  getPerformanceChoicesApi,
  exportPerformanceToExcelApi,
  generatePerformanceReportApi,
  batchDeletePerformanceApi,

  batchOperatePerformanceApi,
  recalculatePerformanceApi,
  getPerformanceTrendApi,
  getDepartmentComparisonApi,
  getPerformanceRankingApi,
};
