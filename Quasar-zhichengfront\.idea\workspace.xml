<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="3ae13988-9d88-4bd6-900a-8c48d6cea04a" name="更改" comment="编写一个用户注册窗口，在向后端提交注册表单，成功后返回登陆窗口，刚注册用户默认密码为123456，状态为禁用，提供文件代码">
      <change afterPath="$PROJECT_DIR$/.editorconfig" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.env" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.gitignore" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.npmrc" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.prettierrc.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.vscode/extensions.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.vscode/launch.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/.vscode/settings.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/README.md" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/eslint.config.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/index.html" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/package-lock.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/package.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/postcss.config.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/favicon.ico" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/icons/favicon-128x128.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/icons/favicon-16x16.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/icons/favicon-32x32.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/icons/favicon-96x96.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/icons/favicon.svg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/icons/favicon0.svg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/icons/favicon32.svg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/resource/10.svg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/resource/4.svg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/resource/5.svg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/resource/6.svg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/resource/7.svg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/resource/8.svg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/resource/9.svg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/resource/inventory.svg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/resource/loading.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/resource/loading.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/resource/loginback.jpg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/resource/loginback.png" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/resource/navigation.svg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/resource/queue.svg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/public/resource/work.svg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/quasar.config.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/App.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/api/billinfo.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/api/services/user.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/assets/quasar-logo-vertical.svg" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/boot/.gitkeep" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/boot/axios.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/boot/bus.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/boot/directives.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/boot/i18n.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/boot/index.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/boot/quasar-plugin-defaults.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/boot/router/layout.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/boot/router/permission.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/BaseContent/BaseContent.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/Breadcrumbs/Breadcrumbs.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/CommonGrid/types.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/Drawer/Drawer.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/Layout/Layout.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/Login/CornerBottom.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/Login/LoginPanel.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/Menu/BaseMenu.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/Menu/BaseMenuItem.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/Skelton/BaseSkelton.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/Tagview/Tagview.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/Toolbar/DarkMode.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/Toolbar/LangSelector.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/Toolbar/ToolbarItem.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/Toolbar/ToolbarTitle.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/common/ImageViewer.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/customer/CustomerDialog.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/customer/customerSelectDialog.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/printprocess/printprocess.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/components/printprocess/printprocgroup.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/composables/eCharts.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/composables/fetch.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/composables/myApi.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/composables/permission.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/composables/useTable.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/composables/useToast.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/css/app.scss" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/css/main.css" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/css/quasar.variables.scss" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/data/Indentinfo.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/data/billinfo.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/data/inputItem.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/directives/index.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/directives/permission.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/env.d.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/i18n/en-US/index.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/i18n/index.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/i18n/zh-CN/index.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/i18n/zh-TW/index.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/layouts/MainLayout.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/ErrorNotFound.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/Login.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/register.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/setPassword/SetPassword.vue" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/quasar.d.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/router/constantRoutes.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/router/index.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/router/routes.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/router/utils/permissionUtils.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/shims-vue.d.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/stores/InputdataStore.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/stores/app.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/stores/breadcrumbs.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/stores/checkboxStore.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/stores/example-store.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/stores/index.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/stores/keep-alive.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/stores/menuStore.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/stores/permission.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/stores/selection-store.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/stores/store-flag.d.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/stores/tagView.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/stores/theme.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/stores/useFormDataCounts.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/stores/user.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/types/enum.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/types/index.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/utils/events.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/utils/export.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/utils/index.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/utils/typeof.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/tsconfig.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/yarn.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/schedule/ScheduleInput.vue" beforeDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="JavaScript File" />
        <option value="CSS File" />
        <option value="tsconfig.json" />
        <option value="TypeScript File" />
        <option value="Vue Composition API Component" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="JsbtTreeLayoutManager">
    <layout place="tools.popupnpm">
      <scroll-view-position x="0" y="0" />
    </layout>
  </component>
  <component name="ProblemsViewState">
    <option name="showPreview" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2vctbd4k6fkbh6DaMITNDUKlpvH" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ASKED_MARK_IGNORED_FILES_AS_EXCLUDED&quot;: &quot;true&quot;,
    &quot;JavaScript 调试.mydebug.executor&quot;: &quot;Debug&quot;,
    &quot;Node.js.index.ts.executor&quot;: &quot;Debug&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;last_opened_file_path&quot;: &quot;I:/WEBKaiFaWORK/Django-ZhiChengERP-WebSystem-main/Quasar-zhichengfront/index.html&quot;,
    &quot;list.type.of.created.stylesheet&quot;: &quot;CSS&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;yarn&quot;,
    &quot;npm.dev.executor&quot;: &quot;Debug&quot;,
    &quot;prettierjs.PrettierConfiguration.Package&quot;: &quot;I:\\WEBKaiFaWORK\\Django-ZhiChengERP-WebSystem-main\\Quasar-zhichengfront\\node_modules\\prettier&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;terminal&quot;,
    &quot;ts.external.directory.path&quot;: &quot;I:\\WEBKaiFaWORK\\Django-ZhiChengERP-WebSystem-main\\Quasar-zhichengfront\\node_modules\\typescript\\lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;vue.recent.templates&quot;: [
      &quot;Vue Composition API Component&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="MoveFile.RECENT_KEYS">
      <recent name="I:\WEBKaiFaWORK\Django-ZhiChengERP-WebSystem-main\Quasar-zhichengfront\src\components\common" />
      <recent name="I:\WEBKaiFaWORK\Django-ZhiChengERP-WebSystem-main\Quasar-zhichengfront\src\pages" />
      <recent name="I:\WEBKaiFaWORK\Django-ZhiChengERP-WebSystem-main\Quasar-zhichengfront\src\components" />
      <recent name="I:\WEBKaiFaWORK\Django-ZhiChengERP-WebSystem-main\Quasar-zhichengfront\src\components\CommonGrid" />
      <recent name="I:\WEBKaiFaWORK\Django-ZhiChengERP-WebSystem-main\Quasar-zhichengfront\src\components\CommonDialog" />
    </key>
  </component>
  <component name="RunManager" selected="JavaScript 调试.mydebug">
    <configuration name="mydebug" type="JavascriptDebugType" uri="http://127.0.0.1:9000">
      <method v="2" />
    </configuration>
    <configuration name="index.ts" type="NodeJSConfigurationType" temporary="true" nameIsGenerated="true" path-to-js-file="index.ts" typescript-loader="bundled" working-dir="$PROJECT_DIR$/src/types">
      <method v="2" />
    </configuration>
    <configuration name="dev" type="js.build_tools.npm">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="JavaScript 调试.mydebug" />
      <item itemvalue="Node.js.index.ts" />
      <item itemvalue="npm.dev" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Node.js.index.ts" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-822845ee3bb5-JavaScript-WS-243.23654.157" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="3ae13988-9d88-4bd6-900a-8c48d6cea04a" name="更改" comment="" />
      <created>1744456262776</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1744456262776</updated>
      <workItem from="1744456264173" duration="58569000" />
      <workItem from="1744636027625" duration="8512000" />
      <workItem from="1744724212019" duration="6052000" />
      <workItem from="1744798417361" duration="16322000" />
      <workItem from="1744889876475" duration="19017000" />
      <workItem from="1744972757814" duration="15709000" />
      <workItem from="1745059233303" duration="15044000" />
      <workItem from="1745081518666" duration="2495000" />
      <workItem from="1745107867818" duration="47686000" />
      <workItem from="1745230838606" duration="18014000" />
      <workItem from="1745316312779" duration="19423000" />
      <workItem from="1745337853539" duration="5694000" />
      <workItem from="1745412196678" duration="14310000" />
      <workItem from="1745490018871" duration="5102000" />
      <workItem from="1745497250556" duration="17447000" />
      <workItem from="1745575786993" duration="17317000" />
      <workItem from="1745627378553" duration="50950000" />
      <workItem from="1745835514262" duration="609000" />
      <workItem from="1745840319378" duration="16904000" />
      <workItem from="1745921812532" duration="22985000" />
      <workItem from="1746007505803" duration="24795000" />
      <workItem from="1746056277207" duration="43551000" />
      <workItem from="1746112115976" duration="19998000" />
      <workItem from="1746161465632" duration="37753000" />
      <workItem from="1746209961728" duration="15000" />
      <workItem from="1746266806800" duration="13432000" />
      <workItem from="1746313450025" duration="46812000" />
      <workItem from="1746399624685" duration="1712000" />
      <workItem from="1746439384289" duration="6515000" />
      <workItem from="1746457509553" duration="5620000" />
      <workItem from="1746530804017" duration="13271000" />
      <workItem from="1746573153145" duration="752000" />
      <workItem from="1746578177718" duration="29813000" />
      <workItem from="1746697945478" duration="11909000" />
      <workItem from="1746840617329" duration="87628000" />
      <workItem from="1747045720961" duration="17578000" />
      <workItem from="1747141127898" duration="11958000" />
      <workItem from="1747218052429" duration="1681000" />
      <workItem from="1747228324295" duration="4988000" />
      <workItem from="1747304286700" duration="15602000" />
      <workItem from="1747389719917" duration="14397000" />
      <workItem from="1747446348060" duration="5713000" />
      <workItem from="1747456452107" duration="28421000" />
      <workItem from="1747571852552" duration="12116000" />
      <workItem from="1747658242727" duration="10084000" />
      <workItem from="1747744984140" duration="9152000" />
      <workItem from="1747833405962" duration="8633000" />
      <workItem from="1747918479032" duration="5822000" />
      <workItem from="1748009252179" duration="4146000" />
      <workItem from="1748049319470" duration="27039000" />
      <workItem from="1748132008711" duration="13915000" />
      <workItem from="1748219848067" duration="6230000" />
      <workItem from="1748257479736" duration="630000" />
      <workItem from="1748308157811" duration="11267000" />
      <workItem from="1748392582693" duration="63000" />
      <workItem from="1748392655745" duration="12776000" />
      <workItem from="1748480000083" duration="3856000" />
      <workItem from="1748555399232" duration="2366000" />
      <workItem from="1748622819870" duration="1499000" />
      <workItem from="1748663722827" duration="26766000" />
      <workItem from="1748738457530" duration="41563000" />
      <workItem from="1748826554497" duration="39410000" />
      <workItem from="1748946018052" duration="22354000" />
      <workItem from="1749032938126" duration="330000" />
      <workItem from="1749033277285" duration="18477000" />
      <workItem from="1749119211904" duration="23200000" />
      <workItem from="1749205257835" duration="2125000" />
      <workItem from="1749207975773" duration="16193000" />
      <workItem from="1749255799147" duration="59902000" />
      <workItem from="1749346473439" duration="30202000" />
      <workItem from="1749464860144" duration="20470000" />
      <workItem from="1749551932532" duration="16357000" />
      <workItem from="1749647430018" duration="3489000" />
      <workItem from="1749688660323" duration="25479000" />
      <workItem from="1749811183611" duration="15435000" />
      <workItem from="1749861149273" duration="28580000" />
      <workItem from="1749948589208" duration="1665000" />
      <workItem from="1749953083925" duration="28805000" />
      <workItem from="1750069176094" duration="18980000" />
      <workItem from="1750156229440" duration="19409000" />
      <workItem from="1750242733846" duration="22633000" />
      <workItem from="1750328512088" duration="11935000" />
      <workItem from="1750344474097" duration="13000" />
      <workItem from="1750347942541" duration="3778000" />
      <workItem from="1750415197967" duration="19517000" />
      <workItem from="1750464623779" duration="40345000" />
      <workItem from="1750551581860" duration="12845000" />
      <workItem from="1750608524986" duration="4279000" />
      <workItem from="1750698839884" duration="1945000" />
      <workItem from="1750769175729" duration="11546000" />
      <workItem from="1750849491183" duration="14395000" />
      <workItem from="1750943533041" duration="12276000" />
      <workItem from="1751030174003" duration="12090000" />
      <workItem from="1751106024240" duration="21636000" />
      <workItem from="1751155757056" duration="39818000" />
      <workItem from="1751278948577" duration="11849000" />
      <workItem from="1751376739821" duration="10360000" />
      <workItem from="1751451554236" duration="18044000" />
      <workItem from="1751548099216" duration="12021000" />
      <workItem from="1751625976089" duration="14317000" />
      <workItem from="1751712097988" duration="15071000" />
      <workItem from="1751764255119" duration="29895000" />
      <workItem from="1751885134618" duration="16421000" />
      <workItem from="1751975644179" duration="7179000" />
      <workItem from="1752056622845" duration="9427000" />
      <workItem from="1752070301740" duration="83000" />
      <workItem from="1752074459912" duration="5933000" />
      <workItem from="1752142844105" duration="6004000" />
      <workItem from="1752293082190" duration="12286000" />
      <workItem from="1752308415346" duration="4727000" />
      <workItem from="1752319209913" duration="4000" />
      <workItem from="1752322269534" duration="736000" />
      <workItem from="1752323070836" duration="4226000" />
      <workItem from="1752330447257" duration="45558000" />
      <workItem from="1752430180784" duration="2000" />
      <workItem from="1752487774423" duration="21337000" />
      <workItem from="1752533997813" duration="841000" />
      <workItem from="1752593074154" duration="5662000" />
      <workItem from="1752666240393" duration="17132000" />
      <workItem from="1752746960192" duration="9062000" />
      <workItem from="1752838910470" duration="10705000" />
      <workItem from="1752895098751" duration="2425000" />
      <workItem from="1752911304871" duration="2993000" />
      <workItem from="1752935165045" duration="34581000" />
      <workItem from="1753034954939" duration="98000" />
      <workItem from="1753094797661" duration="18767000" />
      <workItem from="1753178910561" duration="10392000" />
      <workItem from="1753275728265" duration="7569000" />
      <workItem from="1753363792089" duration="761000" />
      <workItem from="1753365026694" duration="377000" />
      <workItem from="1753366819886" duration="31000" />
      <workItem from="1753368670491" duration="4000" />
      <workItem from="1753377669303" duration="752000" />
      <workItem from="1753579408756" duration="15373000" />
      <workItem from="1753698134214" duration="4285000" />
      <workItem from="1753705059899" duration="14413000" />
      <workItem from="1753784916235" duration="7420000" />
      <workItem from="1753877045450" duration="8031000" />
      <workItem from="1753961126633" duration="12710000" />
      <workItem from="1754043400870" duration="19913000" />
      <workItem from="1754099193878" duration="47556000" />
      <workItem from="1754194841276" duration="36004000" />
      <workItem from="1754303288954" duration="23051000" />
      <workItem from="1754352384435" duration="793000" />
      <workItem from="1754399542007" duration="14853000" />
      <workItem from="1754474956287" duration="15754000" />
      <workItem from="1754497980772" duration="3638000" />
      <workItem from="1754573443919" duration="9914000" />
      <workItem from="1754649804005" duration="17149000" />
      <workItem from="1754735470180" duration="17763000" />
      <workItem from="1754784925292" duration="41795000" />
      <workItem from="1754907729400" duration="20749000" />
      <workItem from="1754993290646" duration="19072000" />
      <workItem from="1755080474994" duration="16768000" />
      <workItem from="1755263154595" duration="8878000" />
      <workItem from="1755306609123" duration="14024000" />
      <workItem from="1755394016364" duration="29864000" />
      <workItem from="1755516705549" duration="14023000" />
      <workItem from="1755597809473" duration="13623000" />
      <workItem from="1755685093750" duration="17037000" />
      <workItem from="1755770708249" duration="1971000" />
      <workItem from="1755858958390" duration="11298000" />
      <workItem from="1755924068689" duration="33198000" />
      <workItem from="1756028053862" duration="13040000" />
      <workItem from="1756120444525" duration="1189000" />
      <workItem from="1756206279666" duration="21723000" />
      <workItem from="1756292201533" duration="11515000" />
      <workItem from="1756375339111" duration="8076000" />
    </task>
    <task id="LOCAL-00001" summary="编写一个用户注册窗口，在向后端提交注册表单，成功后返回登陆窗口，刚注册用户默认密码为123456，状态为禁用，提供文件代码">
      <option name="closed" value="true" />
      <created>1752397277301</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1752397277301</updated>
    </task>
    <option name="localTasksCounter" value="2" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="编写一个用户注册窗口，在向后端提交注册表单，成功后返回登陆窗口，刚注册用户默认密码为123456，状态为禁用，提供文件代码" />
    <option name="LAST_COMMIT_MESSAGE" value="编写一个用户注册窗口，在向后端提交注册表单，成功后返回登陆窗口，刚注册用户默认密码为123456，状态为禁用，提供文件代码" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="javascript">
          <url>http://127.0.0.1:9000/src/pages/navigation.vue?vue&amp;type=style&amp;index=0&amp;scoped=5f342443&amp;lang.css</url>
          <option name="timeStamp" value="168" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/api/services/user.ts</url>
          <line>5</line>
          <option name="timeStamp" value="272" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/api/services/user.ts</url>
          <line>13</line>
          <option name="timeStamp" value="273" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/boot/i18n.ts</url>
          <line>22</line>
          <option name="timeStamp" value="305" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/components/Tagview/Tagview.vue</url>
          <option name="timeStamp" value="311" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/composables/fetch.ts</url>
          <line>22</line>
          <option name="timeStamp" value="313" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/composables/fetch.ts</url>
          <line>18</line>
          <option name="timeStamp" value="314" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/composables/fetch.ts</url>
          <line>9</line>
          <option name="timeStamp" value="315" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/directives/permission.ts</url>
          <line>13</line>
          <option name="timeStamp" value="316" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/layouts/MainLayout.vue</url>
          <line>221</line>
          <option name="timeStamp" value="317" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/layouts/MainLayout.vue</url>
          <line>212</line>
          <option name="timeStamp" value="318" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/stores/tagView.ts</url>
          <line>487</line>
          <option name="timeStamp" value="337" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/stores/tagView.ts</url>
          <line>519</line>
          <option name="timeStamp" value="338" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>mock://file:///I:/WEBKaiFaWORK/quasar/quasar-admin-vue3-typescript-main/quasar-admin-vue3-typescript-main/src/components/Menu/BaseMenuItem.vue</url>
          <line>76</line>
          <option name="timeStamp" value="349" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>mock://file:///I:/WEBKaiFaWORK/quasar/quasar-admin-vue3-typescript-main/quasar-admin-vue3-typescript-main/src/components/Menu/BaseMenuItem.vue</url>
          <line>64</line>
          <option name="timeStamp" value="351" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>mock://file:///I:/WEBKaiFaWORK/quasar/quasar-admin-vue3-typescript-main/quasar-admin-vue3-typescript-main/src/components/Menu/BaseMenuItem.vue</url>
          <line>67</line>
          <option name="timeStamp" value="352" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>mock://file:///I:/WEBKaiFaWORK/quasar/quasar-admin-vue3-typescript-main/quasar-admin-vue3-typescript-main/src/components/Menu/BaseMenuItem.vue</url>
          <line>3</line>
          <option name="timeStamp" value="353" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/components/printprocess/printprocess.vue</url>
          <line>154</line>
          <option name="timeStamp" value="416" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/components/TimePicker/TimePicker.vue</url>
          <line>30</line>
          <option name="timeStamp" value="431" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/directives/permission.ts</url>
          <line>8</line>
          <option name="timeStamp" value="442" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/layouts/MainLayout.vue</url>
          <line>162</line>
          <option name="timeStamp" value="447" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/layouts/MainLayout.vue</url>
          <line>181</line>
          <option name="timeStamp" value="448" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/components/Login/LoginPanel.vue</url>
          <option name="timeStamp" value="461" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/api/django-adapter.ts</url>
          <line>5</line>
          <option name="timeStamp" value="466" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>mock://http://127.0.0.1:9000/@vite/client</url>
          <line>477</line>
          <option name="timeStamp" value="501" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/pages/setPassword/SetPassword.vue</url>
          <line>91</line>
          <option name="timeStamp" value="507" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/components/employee/EmployeeDialog.vue</url>
          <line>478</line>
          <option name="timeStamp" value="514" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/pages/register.vue</url>
          <line>236</line>
          <option name="timeStamp" value="517" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/stores/user.ts</url>
          <line>54</line>
          <option name="timeStamp" value="520" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/components/common/SetPasswordDialog.vue</url>
          <line>120</line>
          <option name="timeStamp" value="524" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/api/idGenerator.ts</url>
          <line>216</line>
          <option name="timeStamp" value="527" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/pages/register.vue</url>
          <line>422</line>
          <option name="timeStamp" value="530" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/pages/Login.vue</url>
          <line>46</line>
          <option name="timeStamp" value="531" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/pages/Login.vue</url>
          <line>80</line>
          <option name="timeStamp" value="533" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/components/orders/OrderCreateDialog.vue</url>
          <line>792</line>
          <option name="timeStamp" value="537" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/components/orders/OrderCreateDialog.vue</url>
          <line>850</line>
          <option name="timeStamp" value="539" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/components/orders/OrderCreateDialog.vue</url>
          <line>890</line>
          <option name="timeStamp" value="540" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/components/orders/OrderCreateDialog.vue</url>
          <line>920</line>
          <option name="timeStamp" value="541" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/components/orders/OrderCreateDialog.vue</url>
          <line>952</line>
          <option name="timeStamp" value="542" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/components/orders/OrderCreateDialog.vue</url>
          <line>959</line>
          <option name="timeStamp" value="543" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/components/orders/OrderCreateDialog.vue</url>
          <line>974</line>
          <option name="timeStamp" value="544" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/components/orders/OrderCreateDialog.vue</url>
          <line>1010</line>
          <option name="timeStamp" value="545" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/components/orders/OrderCreateDialog.vue</url>
          <line>1043</line>
          <option name="timeStamp" value="546" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/components/orders/OrderCreateDialog.vue</url>
          <line>1073</line>
          <option name="timeStamp" value="547" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/components/orders/OrderCreateDialog.vue</url>
          <line>1083</line>
          <option name="timeStamp" value="548" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/components/orders/OrderCreateDialog.vue</url>
          <line>1068</line>
          <option name="timeStamp" value="549" />
        </line-breakpoint>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/components/customer/CustomerDialog.vue</url>
          <line>1</line>
          <option name="timeStamp" value="560" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>