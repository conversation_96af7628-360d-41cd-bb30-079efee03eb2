<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="50a7bfd5-9a7f-4af3-a1b5-89519e4cfc78" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="DjangoConsoleOptions" custom-start-script="import sys; print('Python %s on %s' % (sys.version, sys.platform))&#10;import django; print('Django %s' % django.get_version())&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;if 'setup' in dir(django): django.setup()&#10;import django_manage_shell; django_manage_shell.run(PROJECT_ROOT)">
    <option name="myCustomStartScript" value="import sys; print('Python %s on %s' % (sys.version, sys.platform))&#10;import django; print('Django %s' % django.get_version())&#10;sys.path.extend([WORKING_DIR_AND_PYTHON_PATHS])&#10;if 'setup' in dir(django): django.setup()&#10;import django_manage_shell; django_manage_shell.run(PROJECT_ROOT)" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Python Stub" />
        <option value="HTML File" />
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/.venv/Lib/site-packages/rest_framework/request.py" root0="SKIP_INSPECTION" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 4
}</component>
  <component name="ProjectId" id="2tQFJhY7ijOnzVtCTU45W8j0QC8" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;DefaultHtmlFileTemplate&quot;: &quot;HTML File&quot;,
    &quot;Django Server.ZhiChengERP.executor&quot;: &quot;Run&quot;,
    &quot;Django 服务器.ZhiChengERP.executor&quot;: &quot;Debug&quot;,
    &quot;Python.serializers.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.OpenDjangoStructureViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.pycharm.django.structure.promotion.once.per.project&quot;: &quot;true&quot;,
    &quot;django.template.preview.state&quot;: &quot;SHOW_EDITOR_AND_PREVIEW&quot;,
    &quot;last_opened_file_path&quot;: &quot;I:/WEBKaiFaWORK/Django-ZhiChengERP-WebSystem-main/ZhiChengERP&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;terminal&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="J:\WEB KaiFa WORK\Django-ZhiChengERP-WebSystem-main\ZhiChengERP\ZhiChengERP\base" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="J:\WEB KaiFa WORK\Django-ZhiChengERP-WebSystem-main\ZhiChengERP\extensions" />
      <recent name="J:\WEB KaiFa WORK\Django-ZhiChengERP-WebSystem-main\ZhiChengERP\Apps" />
      <recent name="J:\WEB KaiFa WORK\Django-ZhiChengERP-WebSystem-main\ZhiChengERP\Apps\api" />
      <recent name="J:\WEB KaiFa WORK\Django-ZhiChengERP-WebSystem-main\ZhiChengERP\ZhiChengERP" />
    </key>
  </component>
  <component name="RunManager" selected="Django 服务器.ZhiChengERP">
    <configuration name="serializers" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="ZhiChengERP" />
      <option name="ENV_FILES" value="" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/ZhiChengERP" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/ZhiChengERP/serializers.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="ZhiChengERP" type="Python.DjangoServer" factoryName="Django server">
      <module name="ZhiChengERP" />
      <option name="ENV_FILES" value="$PROJECT_DIR$/conf/.env" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
        <env name="DJANGO_SETTINGS_MODULE" value="ZhiChengERP.settings" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="I:\WEBKaiFaWORK\Django-ZhiChengERP-WebSystem-main\ZhiChengERP" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="launchJavascriptDebuger" value="false" />
      <option name="port" value="8000" />
      <option name="host" value="" />
      <option name="additionalOptions" value="" />
      <option name="browserUrl" value="" />
      <option name="runTestServer" value="false" />
      <option name="runNoReload" value="false" />
      <option name="useCustomRunCommand" value="false" />
      <option name="customRunCommand" value="" />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Django 服务器.ZhiChengERP" />
      <item itemvalue="Python.serializers" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="Python.serializers" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-76f8388c3a79-JavaScript-PY-243.24978.54" />
        <option value="bundled-python-sdk-91e3b7efe1d4-466328ff949b-com.jetbrains.pycharm.pro.sharedIndexes.bundled-PY-243.24978.54" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="50a7bfd5-9a7f-4af3-a1b5-89519e4cfc78" name="更改" comment="" />
      <created>1740276320880</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1740276320880</updated>
      <workItem from="1740276321904" duration="28235000" />
      <workItem from="1740392567430" duration="3501000" />
      <workItem from="1740565633361" duration="9528000" />
      <workItem from="1740651078675" duration="13308000" />
      <workItem from="1740741959392" duration="11796000" />
      <workItem from="1740823997613" duration="21640000" />
      <workItem from="1740871084983" duration="16934000" />
      <workItem from="1740914987267" duration="12387000" />
      <workItem from="1740997486043" duration="10298000" />
      <workItem from="1741022535921" duration="41000" />
      <workItem from="1741022732912" duration="31000" />
      <workItem from="1741083875305" duration="17436000" />
      <workItem from="1741172279464" duration="11056000" />
      <workItem from="1741259654967" duration="7689000" />
      <workItem from="1741342526208" duration="9978000" />
      <workItem from="1741366291550" duration="4000" />
      <workItem from="1741401857550" duration="75358000" />
      <workItem from="1741604522998" duration="3637000" />
      <workItem from="1741611762800" duration="836000" />
      <workItem from="1741612645304" duration="297000" />
      <workItem from="1741612952869" duration="9435000" />
      <workItem from="1741687476372" duration="18599000" />
      <workItem from="1741774502446" duration="20203000" />
      <workItem from="1741861363507" duration="20118000" />
      <workItem from="1741947137290" duration="15708000" />
      <workItem from="1741997796690" duration="43592000" />
      <workItem from="1742086460995" duration="12596000" />
      <workItem from="1742101130232" duration="475000" />
      <workItem from="1742101663523" duration="4882000" />
      <workItem from="1742217072576" duration="1356000" />
      <workItem from="1742306302977" duration="2032000" />
      <workItem from="1742813952739" duration="752000" />
      <workItem from="1742814718970" duration="1911000" />
      <workItem from="1742816652858" duration="14903000" />
      <workItem from="1742908221491" duration="183000" />
      <workItem from="1742908422579" duration="11518000" />
      <workItem from="1742984793392" duration="10870000" />
      <workItem from="1743084636226" duration="878000" />
      <workItem from="1746365865740" duration="835000" />
      <workItem from="1746400440691" duration="2000" />
      <workItem from="1746579075404" duration="2000" />
      <workItem from="1746614760872" duration="3000" />
      <workItem from="1746924652813" duration="15000" />
      <workItem from="1749140050620" duration="9000" />
      <workItem from="1749258571134" duration="3000" />
      <workItem from="1749296699578" duration="2000" />
      <workItem from="1749969916180" duration="6000" />
      <workItem from="1750333802696" duration="2000" />
      <workItem from="1751120311802" duration="2000" />
      <workItem from="1751128172178" duration="2000" />
      <workItem from="1751170566076" duration="2000" />
      <workItem from="1752747625885" duration="11288000" />
      <workItem from="1752774234692" duration="37000" />
      <workItem from="1752794673159" duration="401000" />
      <workItem from="1752841818124" duration="3098000" />
      <workItem from="1752904445169" duration="663000" />
      <workItem from="1753095573356" duration="9777000" />
      <workItem from="1753179337135" duration="601000" />
      <workItem from="1753365374462" duration="20000" />
      <workItem from="1753601511463" duration="8189000" />
      <workItem from="1753698641497" duration="2639000" />
      <workItem from="1753705478983" duration="1254000" />
      <workItem from="1753786456479" duration="5032000" />
      <workItem from="1753876874715" duration="6310000" />
      <workItem from="1753961167933" duration="6590000" />
      <workItem from="1754043548741" duration="5809000" />
      <workItem from="1754099220172" duration="23444000" />
      <workItem from="1754194872819" duration="31889000" />
      <workItem from="1754303319168" duration="11448000" />
      <workItem from="1754352487995" duration="667000" />
      <workItem from="1754399574298" duration="1954000" />
      <workItem from="1754474984435" duration="2016000" />
      <workItem from="1754574265639" duration="672000" />
      <workItem from="1754649805440" duration="1402000" />
      <workItem from="1754735567010" duration="3838000" />
      <workItem from="1754784824599" duration="10199000" />
      <workItem from="1754907763470" duration="8289000" />
      <workItem from="1754993573200" duration="12558000" />
      <workItem from="1755080464000" duration="3069000" />
      <workItem from="1755263174205" duration="2487000" />
      <workItem from="1755306663394" duration="2331000" />
      <workItem from="1755393995913" duration="2435000" />
      <workItem from="1755516716579" duration="1328000" />
      <workItem from="1755599798110" duration="1322000" />
      <workItem from="1755685283411" duration="1865000" />
      <workItem from="1755858977218" duration="2978000" />
      <workItem from="1755923927971" duration="7117000" />
      <workItem from="1756028340385" duration="2097000" />
      <workItem from="1756120426558" duration="1592000" />
      <workItem from="1756206430982" duration="4453000" />
      <workItem from="1756292481938" duration="10717000" />
      <workItem from="1756375430754" duration="6368000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/apps/orders/urls.py</url>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/apps/dashboard/views.py</url>
          <line>222</line>
          <option name="timeStamp" value="18" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/apps/dashboard/views.py</url>
          <line>550</line>
          <option name="timeStamp" value="20" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/apps/dashboard/views.py</url>
          <line>551</line>
          <option name="timeStamp" value="21" />
        </line-breakpoint>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/utils/id_generators.py</url>
          <line>551</line>
          <option name="timeStamp" value="22" />
        </line-breakpoint>
      </breakpoints>
      <default-breakpoints>
        <breakpoint enabled="true" type="django-exception">
          <properties exception="VariableDoesNotExist" />
        </breakpoint>
      </default-breakpoints>
    </breakpoint-manager>
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/ZhiChengERP$serializers.coverage" NAME="serializers 覆盖结果" MODIFIED="1742101140835" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="false" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$/ZhiChengERP" />
  </component>
</project>